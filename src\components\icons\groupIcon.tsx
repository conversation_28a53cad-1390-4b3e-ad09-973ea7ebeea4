function GroupIcon({ className }: { className?: string }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      className={className}
    >
      <path
        d="M5.234 7.73C5.95509 6.58755 6.95376 5.64631 8.13687 4.99408C9.31999 4.34185 10.649 3.99985 12 4C13.3512 3.99969 14.6804 4.3416 15.8637 4.99384C17.047 5.64608 18.0458 6.5874 18.767 7.73L20.457 6.66C19.5554 5.23247 18.307 4.05647 16.8282 3.24163C15.3494 2.42679 13.6884 1.99964 12 2C10.3118 1.99981 8.65093 2.42703 7.17234 3.24187C5.69375 4.0567 4.44552 5.23261 3.544 6.66L5.234 7.73ZM12 20C10.649 20.0001 9.31999 19.6582 8.13687 19.0059C6.95376 18.3537 5.95509 17.4125 5.234 16.27L3.544 17.34C4.44552 18.7674 5.69375 19.9433 7.17234 20.7581C8.65093 21.573 10.3118 22.0002 12 22C13.6884 22.0004 15.3494 21.5732 16.8282 20.7584C18.307 19.9435 19.5554 18.7675 20.457 17.34L18.767 16.27C18.0458 17.4126 17.047 18.3539 15.8637 19.0062C14.6804 19.6584 13.3512 20.0003 12 20ZM12 8C12.2652 8 12.5196 8.10536 12.7071 8.29289C12.8946 8.48043 13 8.73478 13 9C13 9.26522 12.8946 9.51957 12.7071 9.70711C12.5196 9.89464 12.2652 10 12 10C11.7348 10 11.4804 9.89464 11.2929 9.70711C11.1054 9.51957 11 9.26522 11 9C11 8.73478 11.1054 8.48043 11.2929 8.29289C11.4804 8.10536 11.7348 8 12 8ZM12 12C12.7956 12 13.5587 11.6839 14.1213 11.1213C14.6839 10.5587 15 9.79565 15 9C15 8.20435 14.6839 7.44129 14.1213 6.87868C13.5587 6.31607 12.7956 6 12 6C11.2044 6 10.4413 6.31607 9.87868 6.87868C9.31607 7.44129 9 8.20435 9 9C9 9.79565 9.31607 10.5587 9.87868 11.1213C10.4413 11.6839 11.2044 12 12 12ZM12 15C11.4696 15 10.9609 15.2107 10.5858 15.5858C10.2107 15.9609 10 16.4696 10 17H8C8 15.9391 8.42143 14.9217 9.17157 14.1716C9.92172 13.4214 10.9391 13 12 13C13.0609 13 14.0783 13.4214 14.8284 14.1716C15.5786 14.9217 16 15.9391 16 17H14C14 16.4696 13.7893 15.9609 13.4142 15.5858C13.0391 15.2107 12.5304 15 12 15ZM3 11C2.73478 11 2.48043 11.1054 2.29289 11.2929C2.10536 11.4804 2 11.7348 2 12C2 12.2652 2.10536 12.5196 2.29289 12.7071C2.48043 12.8946 2.73478 13 3 13C3.26522 13 3.51957 12.8946 3.70711 12.7071C3.89464 12.5196 4 12.2652 4 12C4 11.7348 3.89464 11.4804 3.70711 11.2929C3.51957 11.1054 3.26522 11 3 11ZM0 12C0 11.2044 0.31607 10.4413 0.87868 9.87868C1.44129 9.31607 2.20435 9 3 9C3.79565 9 4.55871 9.31607 5.12132 9.87868C5.68393 10.4413 6 11.2044 6 12C6 12.7956 5.68393 13.5587 5.12132 14.1213C4.55871 14.6839 3.79565 15 3 15C2.20435 15 1.44129 14.6839 0.87868 14.1213C0.31607 13.5587 0 12.7956 0 12ZM20 12C20 11.7348 20.1054 11.4804 20.2929 11.2929C20.4804 11.1054 20.7348 11 21 11C21.2652 11 21.5196 11.1054 21.7071 11.2929C21.8946 11.4804 22 11.7348 22 12C22 12.2652 21.8946 12.5196 21.7071 12.7071C21.5196 12.8946 21.2652 13 21 13C20.7348 13 20.4804 12.8946 20.2929 12.7071C20.1054 12.5196 20 12.2652 20 12ZM21 9C20.2044 9 19.4413 9.31607 18.8787 9.87868C18.3161 10.4413 18 11.2044 18 12C18 12.7956 18.3161 13.5587 18.8787 14.1213C19.4413 14.6839 20.2044 15 21 15C21.7956 15 22.5587 14.6839 23.1213 14.1213C23.6839 13.5587 24 12.7956 24 12C24 11.2044 23.6839 10.4413 23.1213 9.87868C22.5587 9.31607 21.7956 9 21 9Z"
        fill="#AEAEAE"
      />
    </svg>
  );
}

export default GroupIcon;
