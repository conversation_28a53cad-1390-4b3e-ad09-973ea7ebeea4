import { useBackgroundStore } from "@/store";
import CustomerFooter from "./CustomerFooter";

function CustomerLayout({ children }: { children: React.ReactNode }) {
  const { backgroundColor } = useBackgroundStore();

  return (
    <div
      className="min-h-screen w-full flex flex-col"
      style={{
        background: `linear-gradient(to bottom, #CA001A 10%, ${backgroundColor} 60%, black 100%)`,
      }}
    >
      <div className="grow">{children}</div>
      <CustomerFooter />
    </div>
  );
}

export default CustomerLayout;
