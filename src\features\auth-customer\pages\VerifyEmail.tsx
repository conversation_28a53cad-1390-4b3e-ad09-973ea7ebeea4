import CheckEmailIcon from "@/components/icons/checkEmailIcon";
import CustomerAuthLayout from "@/components/layout/CustomerAuthLayout";
import Brand<PERSON><PERSON> from "@/assets/logo.png";
import { Button } from "@/components/ui/button";
import { Link } from "react-router-dom";

function VerifyEmail() {
  return (
    <CustomerAuthLayout>
      {/* Header */}
      <div className="w-full flex justify-center lg:justify-normal px-4 sm:px-6 lg:px-10 py-6 flex-shrink-0">
        <img src={BrandLogo} alt="ub sports" className="h-8 w-auto" />
      </div>

      {/* Main Content - Centered */}
      <div className="flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-10 pb-8">
        <div className="w-full max-w-lg mx-auto rounded-xl lg:backdrop-blur-2xl p-8 sm:p-10">
          <div className="flex flex-col items-center text-center space-y-6">
            <CheckEmailIcon className="w-16 h-16 sm:w-20 sm:h-20" />

            <h1 className="text-2.5xl font-bold text-white leading-tight">
              Check your email
            </h1>

            <p className="text-white/80 text-sm sm:text-base leading-relaxed max-w-sm">
              We’ve sent a link to verify your account. Just click the button in
              the email to activate your profile.
            </p>

            <Link to={"/account-verified"} className="w-full">
              <Button className="w-full" variant="white">
                Check Inbox
              </Button>
            </Link>

            <button className="font-bold text-white hover:text-white/80 transition-colors underline">
              Resend link
            </button>
          </div>
        </div>
      </div>
    </CustomerAuthLayout>
  );
}

export default VerifyEmail;
