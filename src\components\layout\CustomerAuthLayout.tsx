import bg from "../../assets/customer-auth-bg.png";
import mobileBg from "../../assets/customer-auth-bg-mobile.png";

function CustomerAuthLayout({ children }: { children: React.ReactNode }) {
  return (
    <>
      {/* Desktop Layout */}

      <div
        className="bg-cover bg-center bg-no-repeat min-h-screen w-full hidden lg:block overflow-x-hidden"
        style={{ backgroundImage: `url(${bg})` }}
      >
        <div className="w-full min-h-screen flex flex-col">{children}</div>
      </div>

      {/* Mobile Layout */}
      <div
        className="bg-cover bg-center bg-no-repeat min-h-screen w-full lg:hidden block overflow-x-hidden"
        style={{ backgroundImage: `url(${mobileBg})` }}
      >
        <div className="w-full min-h-screen flex flex-col">{children}</div>
      </div>
    </>
  );
}

export default CustomerAuthLayout;
