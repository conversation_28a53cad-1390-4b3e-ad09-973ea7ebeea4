import {
  <PERSON>er,
  <PERSON>er<PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>er<PERSON>ooter,
  <PERSON>er<PERSON><PERSON><PERSON>,
} from "@/components/ui/drawer";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import type { Sport, Experience } from "../type";
import { useState, useEffect } from "react";
import DoubleTickIcon from "@/components/icons/doubleTickIcon";

type SelectSportsDrawerProps = {
  isOpen: boolean;
  onClose: () => void;
  sport: Sport | null;
  onConfirm: (
    sportId: string,
    experience: Experience,
    selected: boolean
  ) => void;
  onRemove: (sportId: string) => void;
};

function SelectSportsDrawer({
  isOpen,
  onClose,
  sport,
  onConfirm,
  onRemove,
}: SelectSportsDrawerProps) {
  // Initialize with sport's current experience or default to "Beginner"
  const [selectedExperience, setSelectedExperience] = useState<Experience>(
    sport?.experienceSelected || "Beginner"
  );

  // Track if user has changed the experience level from the original
  const [hasExperienceChanged, setHasExperienceChanged] = useState(false);

  // Reset state when drawer opens with a new sport
  useEffect(() => {
    if (isOpen && sport) {
      setSelectedExperience(sport.experienceSelected || "Beginner");
      setHasExperienceChanged(false);
    }
  }, [isOpen, sport]);

  if (!sport) return null;

  const experienceLevels: Experience[] = [
    "Beginner",
    "Intermediate",
    "Advanced",
  ];

  const handleConfirm = () => {
    onConfirm(sport._id, selectedExperience, true);
    onCloseDrawer();
  };

  const handleRemove = () => {
    onRemove(sport._id);
    onCloseDrawer();
  };
  const handleExperienceChange = (experience: Experience) => {
    setSelectedExperience(experience);
    // Check if the experience has changed from the original
    const originalExperience = sport.experienceSelected || "Beginner";
    setHasExperienceChanged(experience !== originalExperience);
  };

  // Determine button state based on selection status and experience changes
  const shouldShowSelectButton = () => {
    // If sport is not selected, always show "Select"
    if (!sport.selected) {
      return true;
    }
    // If sport is selected but experience has changed, show "Select"
    if (sport.selected && hasExperienceChanged) {
      return true;
    }
    // If sport is selected and no changes, show "Selected"
    return false;
  };

  const onCloseDrawer = () => {
    setHasExperienceChanged(false);
    onClose();
  };

  return (
    <Drawer open={isOpen} onOpenChange={onCloseDrawer}>
      <DrawerContent className="bg-gradient-to-b from-[#740F1C] to-[#040031] border-none text-white max-h-[80vh]">
        <DrawerHeader className="pb-4">
          <DrawerTitle className="sr-only">Select Sport Experience</DrawerTitle>

          {/* Header with Icon and Sport Info */}
          <div className="flex items-start gap-4 p-4 mx-2 rounded-lg bg-[#FDFDFD1A] backdrop-blur-sm">
            <img
              src={sport.icon}
              alt={sport.name}
              className="object-contain w-16 h-16 my-auto"
            />
            <div className="flex-1 text-left">
              <h2 className="mb-2 text-xl font-bold text-white">
                {sport.name}
              </h2>
              <p className="text-sm leading-relaxed text-white/80">
                {sport.description}
              </p>
            </div>
          </div>
        </DrawerHeader>

        {/* Experience Level Selection */}
        <div className="px-6 pb-6 space-y-6">
          <div className="space-y-4">
            <h3 className="text-sm font-medium text-white/90">
              Experience level
            </h3>

            {/* Experience Level Slider */}
            <div className="relative">
              <div className="flex items-center justify-between mb-4">
                {experienceLevels.map((level) => (
                  <button
                    key={level}
                    onClick={() => handleExperienceChange(level)}
                    className={`text-xs font-medium transition-colors ${
                      selectedExperience === level
                        ? "text-white"
                        : "text-white/50"
                    }`}
                  >
                    {level}
                  </button>
                ))}
              </div>

              {/* Slider Track */}
              <div className="relative">
                <Progress
                  value={
                    selectedExperience === "Beginner"
                      ? 0
                      : selectedExperience === "Intermediate"
                      ? 50
                      : 100
                  }
                  className="h-1 bg-white/20"
                />
                {/* Slider Thumb */}
                <div
                  className="absolute w-4 h-4 bg-white rounded-full -top-1.5 transition-all duration-300 shadow-lg"
                  style={{
                    left:
                      selectedExperience === "Beginner"
                        ? "0"
                        : selectedExperience === "Intermediate"
                        ? "calc(50% - 8px)"
                        : "calc(100% - 8px)",
                  }}
                />
              </div>
            </div>
          </div>
        </div>

        <DrawerFooter className="pt-0">
          {/* Action Buttons */}
          <div className="flex gap-3">
            <Button
              onClick={onCloseDrawer}
              className="flex-1 text-white bg-transparent border-none hover:bg-white/10"
            >
              Discard
            </Button>
            {shouldShowSelectButton() ? (
              <Button
                onClick={handleConfirm}
                variant="white"
                className="flex-1 font-semibold text-blue-900 bg-white hover:bg-white/90"
              >
                Select
              </Button>
            ) : (
              <Button
                onClick={handleRemove}
                className="flex items-center justify-center flex-1 gap-2 font-semibold text-white bg-transparent border border-white/50 hover:bg-white/10 rounded-xl"
              >
                <DoubleTickIcon className="text-white" />
                Unselect
              </Button>
            )}
          </div>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  );
}

export default SelectSportsDrawer;
