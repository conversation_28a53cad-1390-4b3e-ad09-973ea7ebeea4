import PermissionIcon from "@/components/icons/permissionIcon";
import CustomerAuthLayout from "@/components/layout/CustomerAuthLayout";
import Brand<PERSON>ogo from "@/assets/logo.png";
import GuardianForm from "../components/GuardianForm";

function Guardian() {
  return (
    <CustomerAuthLayout>
      {/* Header */}
      <div  className="w-full flex justify-center lg:justify-normal px-4 sm:px-6 lg:px-10 py-6 flex-shrink-0">
        <img src={BrandLogo} alt="ub sports" className="h-8 w-auto" />
      </div>

      {/* Main Content - Centered */}
      <div className="flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-10 pb-8">
        <div className="w-full max-w-lg mx-auto rounded-xl lg:backdrop-blur-2xl p-8 sm:p-10">
          <div className="flex flex-col items-center text-center space-y-6">
            <PermissionIcon className="w-16 h-16 sm:w-20 sm:h-20" />

            <h1 className="text-2.5xl font-bold text-white leading-tight">
              We need your guardian’s permission.{" "}
            </h1>

            <p className="text-white/80 text-sm sm:text-base leading-relaxed max-w-sm">
              To comply with safety laws, we need to contact your parent or
              guardian before continuing.
            </p>
            {/* Form Section */}
            <GuardianForm />
          </div>
        </div>
      </div>
    </CustomerAuthLayout>
  );
}

export default Guardian;
