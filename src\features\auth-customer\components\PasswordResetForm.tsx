import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import type { PasswordResetFormData } from "../type";
import { passwordResetSchema } from "../validation";
import LockIcon from "@/components/icons/lockIcon";
import ShowPasswordIcon from "@/components/icons/showPasswordIcon";
import { useNavigate } from "react-router-dom";
import { useState } from "react";
import HidePasswordIcon from "@/components/icons/hidePasswordIcon";

function PasswordResetForm() {
  const navigate = useNavigate();
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const {
    control,
    handleSubmit,
    formState: { errors, isSubmitting },
    setError,
    clearErrors,
  } = useForm<PasswordResetFormData>({
    defaultValues: {
      password: "",
      confirmPassword: "",
    },
  });

  const validateForm = (data: PasswordResetFormData) => {
    const { error } = passwordResetSchema.validate(data, { abortEarly: false });

    if (error) {
      // Clear previous errors
      clearErrors();

      // Set new errors
      error.details.forEach((detail) => {
        const path = detail.path.join(".");
        setError(path as keyof PasswordResetFormData, {
          type: "validation",
          message: detail.message,
        });
      });
      return false;
    }
    return true;
  };

  const onSubmit = async (data: PasswordResetFormData) => {
    if (!validateForm(data)) {
      return;
    }

    try {
      // Here you would typically send the data to your API

      // Simulate API call
      // await resetPassword(data);

      // Redirect to login page after successful password reset
      navigate("/login");
    } catch {
      // Handle error (show error message, etc.)
    }
  };

  return (
    <div className="bg-container w-full max-w-lg mx-auto rounded-xl lg:backdrop-blur-sm p-4 sm:px-6">
      {/* Header */}
      <div className="text-center mb-8">
        {/* Lock icon */}
        <div className="flex justify-center mb-4">
          <LockIcon />
        </div>
        <h1 className="text-2.5xl font-bold text-white leading-tight">
          Create password
        </h1>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-5 w-full">
        {/* Password */}
        <div>
          <label className="block text-sm font-medium text-white mb-2">
            Password
          </label>
          <div className="relative">
            <Controller
              name="password"
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  type={showPassword ? "text" : "password"}
                  variant="transparent"
                  placeholder="Enter your password"
                  className="text-white pr-12"
                />
              )}
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-3 top-1/2 transform -translate-y-1/2"
              aria-label={showPassword ? "Hide password" : "Show password"}
            >
              {showPassword ? (
                <ShowPasswordIcon className="w-5 h-5 text-gray-400 transition-colors hover:text-white" />
              ) : (
                <HidePasswordIcon className="w-5 h-5 text-gray-400 transition-colors hover:text-white" />
              )}
            </button>
          </div>
          {errors.password && (
            <p className="text-red-300 text-xs mt-1">
              {errors.password.message}
            </p>
          )}
        </div>

        {/* Confirm Password */}
        <div>
          <label className="block text-sm font-medium text-white mb-2">
            Re-type Password
          </label>
          <div className="relative">
            <Controller
              name="confirmPassword"
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  type={showConfirmPassword ? "text" : "password"}
                  variant="transparent"
                  placeholder="Re-enter your password"
                  className="text-white pr-12"
                />
              )}
            />
            <button
              type="button"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              className="absolute right-3 top-1/2 transform -translate-y-1/2"
              aria-label={
                showConfirmPassword
                  ? "Hide confirm password"
                  : "Show confirm password"
              }
            >
              {showPassword ? (
                <ShowPasswordIcon className="w-5 h-5 text-gray-400 transition-colors hover:text-white" />
              ) : (
                <HidePasswordIcon className="w-5 h-5 text-gray-400 transition-colors hover:text-white" />
              )}
            </button>
          </div>
          {errors.confirmPassword && (
            <p className="text-red-300 text-xs mt-1">
              {errors.confirmPassword.message}
            </p>
          )}
        </div>

        {/* Submit Button */}
        <div className="pt-4">
          <Button
            type="submit"
            variant="white"
            size="lg"
            disabled={isSubmitting}
            className="w-full"
          >
            {isSubmitting ? "Resetting..." : "Reset"}
          </Button>
        </div>
      </form>
    </div>
  );
}

export default PasswordResetForm;
