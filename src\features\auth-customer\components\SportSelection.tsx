import SportsCard from "./SportsCard";
import SelectSportsModal from "./SelectSportsModal";
import SelectSportsDrawer from "./SelectSportsDrawer";

import SearchIcon from "@/components/icons/searchIcon";
import { Input } from "@/components/ui/input";
import LeftIcon from "@/components/icons/leftIcon";
import type { Sport, Experience } from "../type";
import { useEffect, useState } from "react";
import { sportsData } from "../data/sports";

// dummy data

function SportSelection() {
  const [sports, setSports] = useState<Sport[]>(sportsData);
  const [modalOpen, setModalOpen] = useState(false);
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [selectedSport, setSelectedSport] = useState<Sport | null>(null);

  const handleSportClick = (sport: Sport) => {
    setSelectedSport(sport);
    // Show modal on desktop (lg+), drawer on mobile
    if (window.innerWidth >= 1024) {
      setModalOpen(true);
    } else {
      setDrawerOpen(true);
    }
  };

  const handleSportConfirm = (
    sportId: string,
    experience: Experience,
    selected: boolean
  ) => {
    setSports((prevSports) =>
      prevSports.map((sport) =>
        sport._id === sportId
          ? {
              ...sport,
              selected,
              experienceSelected: selected ? experience : null,
            }
          : sport
      )
    );
  };

  const removeSelection = (sportId: string) => {
    setSports((prevSports) => {
      return prevSports.map((sport) => {
        if (sport._id === sportId) {
          return {
            ...sport,
            selected: false,
            experienceSelected: null,
          };
        }
        return sport;
      });
    });
  };
  const handleModalClose = () => {
    setModalOpen(false);
    setSelectedSport(null);
  };

  const handleDrawerClose = () => {
    setDrawerOpen(false);
    setSelectedSport(null);
  };

  useEffect(() => {
    const initialSports = localStorage.getItem("selectedSports");
    if (initialSports) {
      setSports((prevSports) =>
        prevSports.map((sport) => {
          if (sport._id === initialSports) {
            return {
              ...sport,
              selected: true,
              experienceSelected: "Beginner",
            };
          }
          return sport;
        })
      );
    }
  }, []);
  return (
    <div className="p-5 px-8 backdrop-blur-sm bg-container rounded-2xl">
      <div className="justify-between hidden mb-5 lg:flex">
        <div className="flex flex-col gap-y-2">
          <div className="flex items-center gap-x-2">
            <LeftIcon className="text-white" />
            <h1 className="text-2xl font-bold text-white grow">
              Select sports to track
            </h1>
          </div>
          <p className="leading-relaxed text-white-85">
            Your email has been confirmed. Welcome to the UB Sports universe!
          </p>
        </div>
        <div className="relative">
          <SearchIcon className="absolute z-10 -translate-y-1/2 left-4 top-1/3 text-white/60" />
          <Input
            type="text"
            variant="transparent"
            className="w-full pl-12 text-white backdrop-blur-0 placeholder:text-white/60"
          />
        </div>
      </div>
      <div className="grid grid-cols-2 gap-4 lg:grid-cols-6 lg:mt-3">
        {sports.map((sport) => (
          <SportsCard
            key={sport._id}
            _id={sport._id}
            name={sport.name}
            icon={sport.icon}
            description={sport.description}
            experienceSelected={sport.experienceSelected}
            selected={sport.selected}
            onSportClick={handleSportClick}
          />
        ))}
      </div>

      {/* Modal - Only visible on lg+ screens */}
      <SelectSportsModal
        isOpen={modalOpen}
        onClose={handleModalClose}
        sport={selectedSport}
        onConfirm={handleSportConfirm}
        onRemove={removeSelection}
      />

      {/* Drawer - Only visible on mobile screens */}
      <SelectSportsDrawer
        isOpen={drawerOpen}
        onClose={handleDrawerClose}
        sport={selectedSport}
        onConfirm={handleSportConfirm}
        onRemove={removeSelection}
      />
    </div>
  );
}

export default SportSelection;
