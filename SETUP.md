# 🛠️ Setup Instructions

Follow the steps below to run the project locally.

## 📦 Prerequisites

- Node.js 18+
- pnpm or yarn (or use npm)
- Git

## 📥 Installation

```bash
git clone https://github.com/your-org/frontend-template.git
cd frontend-template

npm install      # or yarn / pnpm
npm run dev      # start local server
```
## ✅ Environment Variables

Create a `.env.local` file and copy values from `.env.example`:

```env
VITE_PUBLIC_API_URL=http://localhost:3000/api
```

## 🧪 Testing

```bash
npm run lint
npm run test
```

## 🔄 Common Scripts

| Script          | Description               |
| --------------- | ------------------------- |
| `npm run dev`   | Starts dev server         |
| `npm run build` | Builds for production     |
| `npm run lint`  | Lint checks               |
| `npm run test`  | Run unit tests (if added) |
