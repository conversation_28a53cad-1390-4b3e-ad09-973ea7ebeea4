function VideoIcon({ className }: { className?: string }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="25"
      height="24"
      viewBox="0 0 25 24"
      fill="none"
      className={className}
    >
      <path
        d="M15.2077 12.8056C15.0718 13.3699 14.4274 13.7686 13.1377 14.5651C11.893 15.3355 11.2702 15.7216 10.768 15.5668C10.5571 15.4992 10.3677 15.3772 10.219 15.2131C9.84998 14.8081 9.84998 14.0215 9.84998 12.4501C9.84998 10.8787 9.84998 10.0921 10.219 9.68713C10.372 9.51973 10.561 9.39823 10.768 9.33433C11.2702 9.17863 11.893 9.56473 13.1386 10.3351C14.4274 11.1325 15.0718 11.5312 15.2086 12.0946C15.2644 12.3283 15.2644 12.5719 15.2086 12.8056"
        stroke="#AEAEAE"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M21.0982 11.0999C21.0994 11.5229 21.1 11.9729 21.1 12.4499C21.1 16.4801 21.1 18.4961 19.8481 19.748C18.5962 20.9999 16.5811 20.9999 12.55 20.9999C8.5198 20.9999 6.5038 20.9999 5.2519 19.748C4 18.4961 4 16.481 4 12.4499C4 8.4197 4 6.4037 5.2519 5.1518C6.5038 3.8999 8.5189 3.8999 12.55 3.8999C13.027 3.8999 13.477 3.9005 13.9 3.9017"
        stroke="#AEAEAE"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M18.85 3L19.0822 3.6273C19.3864 4.4499 19.5385 4.8612 19.8382 5.1609C20.1388 5.4615 20.5501 5.6136 21.3727 5.9178L22 6.15L21.3727 6.3822C20.5501 6.6864 20.1388 6.8385 19.8391 7.1382C19.5385 7.4388 19.3864 7.8501 19.0822 8.6727L18.85 9.3L18.6178 8.6727C18.3136 7.8501 18.1615 7.4388 17.8618 7.1391C17.5612 6.8385 17.1499 6.6864 16.3273 6.3822L15.7 6.15L16.3273 5.9178C17.1499 5.6136 17.5612 5.4615 17.8609 5.1618C18.1615 4.8612 18.3136 4.4499 18.6178 3.6273L18.85 3Z"
        stroke="#AEAEAE"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
}

export default VideoIcon;
