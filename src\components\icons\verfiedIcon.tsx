function VerfiedIcon({ className }: { className: string }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="96"
      height="96"
      viewBox="0 0 96 96"
      fill="none"
      className={className}
    >
      <path
        d="M39.1241 15.5601C41.3801 13.6401 42.5081 12.6801 43.6841 12.1161C45.031 11.472 46.5051 11.1377 47.9981 11.1377C49.4911 11.1377 50.9652 11.472 52.3121 12.1161C53.4921 12.6761 54.6201 13.6361 56.8721 15.5601C57.7721 16.3281 58.2201 16.7081 58.7001 17.0281C59.7993 17.7648 61.0338 18.276 62.3321 18.5321C62.8961 18.6441 63.4841 18.6921 64.6601 18.7881C67.6161 19.0201 69.0921 19.1401 70.3241 19.5761C71.7313 20.0727 73.0095 20.8779 74.065 21.9327C75.1205 22.9876 75.9266 24.2652 76.4241 25.6721C76.8601 26.9081 76.9761 28.3841 77.2121 31.3361C77.3041 32.5121 77.3521 33.1001 77.4641 33.6681C77.7201 34.9641 78.2321 36.2001 78.9681 37.2961C79.2881 37.7761 79.6721 38.2241 80.4361 39.1241C82.3561 41.3801 83.3201 42.5081 83.8841 43.6841C84.5282 45.031 84.8625 46.5051 84.8625 47.9981C84.8625 49.4911 84.5282 50.9652 83.8841 52.3121C83.3241 53.4881 82.3601 54.6161 80.4361 56.8721C79.9114 57.4522 79.4213 58.0626 78.9681 58.7001C78.2319 59.7981 77.7207 61.0312 77.4641 62.3281C77.3521 62.8961 77.3041 63.4841 77.2121 64.6601C76.9761 67.6121 76.8601 69.0921 76.4241 70.3241C75.9266 71.7309 75.1205 73.0086 74.065 74.0634C73.0095 75.1183 71.7313 75.9235 70.3241 76.4201C69.0921 76.8601 67.6161 76.9761 64.6601 77.2081C63.4841 77.3041 62.9001 77.3521 62.3321 77.4641C61.0338 77.7202 59.7993 78.2314 58.7001 78.9681C58.0639 79.4214 57.4549 79.9115 56.8761 80.4361C54.6201 82.3561 53.4921 83.3161 52.3161 83.8801C50.9692 84.5242 49.4951 84.8585 48.0021 84.8585C46.5091 84.8585 45.035 84.5242 43.6881 83.8801C42.5081 83.3201 41.3801 82.3601 39.1281 80.4361C38.548 79.9114 37.9376 79.4213 37.3001 78.9681C36.2009 78.2314 34.9663 77.7202 33.6681 77.4641C32.8978 77.3333 32.1204 77.2479 31.3401 77.2081C28.3841 76.9761 26.9081 76.8561 25.6761 76.4201C24.2689 75.9235 22.9907 75.1183 21.9352 74.0634C20.8797 73.0086 20.0736 71.7309 19.5761 70.3241C19.1401 69.0921 19.0241 67.6121 18.7881 64.6601C18.7498 63.8785 18.6656 63.0998 18.5361 62.3281C18.2795 61.0312 17.7683 59.7981 17.0321 58.7001C16.7121 58.2201 16.3281 57.7721 15.5641 56.8721C13.6441 54.6161 12.6801 53.4881 12.1161 52.3121C11.472 50.9652 11.1377 49.4911 11.1377 47.9981C11.1377 46.5051 11.472 45.031 12.1161 43.6841C12.6801 42.5081 13.6401 41.3801 15.5641 39.1241C16.3281 38.2241 16.7121 37.7761 17.0321 37.2961C17.7683 36.1981 18.2795 34.9649 18.5361 33.6681C18.6481 33.1001 18.6961 32.5121 18.7881 31.3361C19.0241 28.3841 19.1401 26.9081 19.5761 25.6721C20.074 24.2648 20.8807 22.9869 21.9369 21.932C22.9932 20.8772 24.2722 20.0722 25.6801 19.5761C26.9121 19.1401 28.3881 19.0201 31.3441 18.7881C32.5201 18.6921 33.1041 18.6441 33.6721 18.5321C34.9703 18.276 36.2049 17.7648 37.3041 17.0281C37.7841 16.7081 38.2281 16.3281 39.1241 15.5601Z"
        stroke="white"
        stroke-width="3"
      />
      <path
        d="M34 50L42 58L62 38"
        stroke="white"
        stroke-width="3"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
}

export default VerfiedIcon;
