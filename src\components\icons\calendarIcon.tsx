function CalendarIcon({ className }: { className?: string }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="25"
      height="24"
      viewBox="0 0 25 24"
      fill="none"
      className={className}
    >
      <path
        d="M8.5 13.5C8.22386 13.5 8 13.2762 8 13C8 12.7239 8.22386 12.5 8.5 12.5H16.5C16.7761 12.5 17 12.7239 17 13C17 13.2762 16.7761 13.5 16.5 13.5H8.5ZM8.5 17.5C8.22386 17.5 8 17.2762 8 17C8 16.7239 8.22386 16.5 8.5 16.5H13.5C13.7761 16.5 14 16.7239 14 17C14 17.2762 13.7761 17.5 13.5 17.5H8.5ZM6.116 21C5.65533 21 5.271 20.846 4.963 20.538C4.655 20.23 4.50067 19.8457 4.5 19.385V6.61502C4.5 6.15502 4.65433 5.77102 4.963 5.46302C5.27167 5.15502 5.656 5.00069 6.116 5.00002H6.885C7.43729 5.00002 7.885 4.5523 7.885 4.00002V3.30852C7.885 3.01111 8.12609 2.77002 8.4235 2.77002C8.72091 2.77002 8.962 3.01111 8.962 3.30852V4.00002C8.962 4.5523 9.40972 5.00002 9.962 5.00002H15.116C15.6683 5.00002 16.116 4.5523 16.116 4.00002V3.27002C16.116 2.99388 16.3399 2.77002 16.616 2.77002C16.8921 2.77002 17.116 2.99388 17.116 3.27002V4.00002C17.116 4.5523 17.5637 5.00002 18.116 5.00002H18.885C19.345 5.00002 19.7293 5.15435 20.038 5.46302C20.3467 5.77169 20.5007 6.15602 20.5 6.61602V19.385C20.5 19.845 20.346 20.2294 20.038 20.538C19.73 20.8467 19.3453 21.0007 18.884 21H6.116ZM6.116 20H18.885C19.0383 20 19.1793 19.936 19.308 19.808C19.4367 19.68 19.5007 19.5387 19.5 19.384V9C19.5 8.44771 19.0523 8 18.5 8H6.5C5.94772 8 5.5 8.44772 5.5 9V19.385C5.5 19.5384 5.564 19.6794 5.692 19.808C5.82 19.9367 5.961 20.0007 6.115 20"
        fill="#AEAEAE"
      />
    </svg>
  );
}

export default CalendarIcon;
