import StarIcon from "@/components/icons/starIcon";
import type { Experience, Sport } from "../type";

type SportsCardProps = {
  _id: string;
  name: string;
  icon: string;
  description: string;
  selected: boolean;
  experienceSelected: Experience | null;
  onSportClick: (sport: Sport) => void;
};

function SportsCard({
  _id,
  name,
  icon,
  description,
  selected,
  experienceSelected,
  onSportClick,
}: SportsCardProps) {
  const handleSelect = () => {
    const sport: Sport = {
      _id,
      name,
      icon,
      description,
      selected,
      experienceSelected,
    };
    onSportClick(sport);
  };
  let level =
    experienceSelected === "Beginner"
      ? 1
      : experienceSelected === "Intermediate"
      ? 2
      : 3;
  if (!experienceSelected) {
    level = 0;
  }
  return (
    <div
      className={`
        ${selected ? "border-2 border-white-50" : "border border-white/[0.12]"}
        relative p-4 lg:p-y-2 lg:px-10 rounded-xl cursor-pointer transition-all duration-200
        backdrop-blur-sm bg-white/[0.08] 
        hover:bg-white/[0.12] hover:border-white/[0.18] hover:scale-[1.02] w-full 
      `}
      onClick={handleSelect}
    >
      {/* Stars - Top Right */}
      <div className="flex justify-end w-full mb-4 lg:ml-5 gap-x-1">
        {Array.from({ length: level }, (_, index) => (
          <StarIcon key={index} />
        ))}
      </div>

      {/* Icon and Name - Center */}
      <div className="flex flex-col items-center justify-center gap-y-4 min-h-[80px]">
        <div className="flex items-center justify-center w-14 h-14">
          <img src={icon} alt={name} className="object-contain w-12 h-12" />
        </div>
        <h3 className="px-1 text-sm font-medium leading-tight text-center text-white">
          {name}
        </h3>
      </div>
    </div>
  );
}

export default SportsCard;
