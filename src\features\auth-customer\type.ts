// Registration form types
export type RegisterFormData = {
  firstName: string;
  lastName: string;
  sport: string;
  email: string;
  phone: string;
  dateOfBirth: {
    day: string;
    month: string;
    year: string;
  };
  location: string;
  zipCode: string;
};

// Login form types
export type LoginFormData = {
  email: string;
  password: string;
}

// Forgot password form types
export type ForgotPasswordFormData = {
  email: string;
}

// Password reset form types
export type PasswordResetFormData = {
  password: string;
  confirmPassword: string;
}

// Sports options
export type SportOption = {
  value: string;
  label: string;
};

// Form validation errors
export type FormErrors = {
  [key: string]: string | undefined;
};

export type GuardianFormData = {
  email?: string;
  phone?: string;
};

export type Experience = "Beginner" | "Intermediate" | "Advanced";

export type Sport = {
  _id: string;
  name: string;
  icon: string;
  description: string;
  selected: boolean;
  experienceSelected: Experience | null;
  category: string;
  color: string;
};
