import { create } from "zustand";

type BackgroundState ={
  backgroundColor: string;
  setBackgroundColor: (color: string) => void;
  resetBackgroundColor: () => void;
}

const DEFAULT_BACKGROUND_COLOR = "#060042";

export const useBackgroundStore = create<BackgroundState>()((set) => ({
  backgroundColor: DEFAULT_BACKGROUND_COLOR,

  setBackgroundColor: (color: string) => {
    set({ backgroundColor: color });
  },

  resetBackgroundColor: () => {
    set({ backgroundColor: DEFAULT_BACKGROUND_COLOR });
  },
}));
