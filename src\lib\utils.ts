import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

type DateOfBirth = {
  day: string;
  month: string;
  year: string;
};

export function calculateAge(dateOfBirth: DateOfBirth): number {
  const { day, month, year } = dateOfBirth;

  const birthDate = new Date(
    Number(year),
    Number(month) - 1, // Month is 0-indexed in JS Date
    Number(day)
  );

  if (isNaN(birthDate.getTime())) {
    throw new Error("Invalid date of birth");
  }

  const today = new Date();
  let age = today.getFullYear() - birthDate.getFullYear();

  const hasHadBirthdayThisYear =
    today.getMonth() > birthDate.getMonth() ||
    (today.getMonth() === birthDate.getMonth() &&
      today.getDate() >= birthDate.getDate());

  if (!hasHadBirthdayThisYear) {
    age -= 1;
  }

  return age;
}

export function customLog<T>(message: string, data?: T[] | T) {
  if (import.meta.env.VITE_API_ENV !== "Development") return;
  if (!data) {
    console.log(message);
    return;
  }
  if (Array.isArray(data)) {
    console.log(message, ...data);
    return;
  }
  console.log(message, data);
}
