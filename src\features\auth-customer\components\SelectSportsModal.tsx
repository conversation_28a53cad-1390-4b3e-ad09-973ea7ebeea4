import { Modal } from "@/components/ui/modal";
import { But<PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import type { Sport, Experience } from "../type";
import { useState, useEffect } from "react";
import DoubleTickIcon from "@/components/icons/doubleTickIcon";

type SelectSportsModalProps = {
  isOpen: boolean;
  onClose: () => void;
  sport: Sport | null;
  onConfirm: (
    sportId: string,
    experience: Experience,
    selected: boolean
  ) => void;
  onRemove: (sportId: string) => void;
};

function SelectSportsModal({
  isOpen,
  onClose,
  sport,
  onConfirm,
  onRemove
}: SelectSportsModalProps) {
  // Initialize with sport's current experience or default to "Beginner"
  const [selectedExperience, setSelectedExperience] = useState<Experience>(
    sport?.experienceSelected || "Beginner"
  );

  // Track if user has changed the experience level from the original
  const [hasExperienceChanged, setHasExperienceChanged] = useState(false);

  // Reset state when modal opens with a new sport
  useEffect(() => {
    if (isOpen && sport) {
      setSelectedExperience(sport.experienceSelected || "Beginner");
      setHasExperienceChanged(false);
    }
  }, [isOpen, sport]);

  if (!sport) return null;
  const experienceLevels: Experience[] = [
    "Beginner",
    "Intermediate",
    "Advanced",
  ];

  const handleConfirm = () => {
    onConfirm(sport._id, selectedExperience, true);
    onCloseModal();
  };

  const handleRemove = () => {
    onRemove(sport._id);
    onCloseModal();
  };
  const handleExperienceChange = (experience: Experience) => {
    setSelectedExperience(experience);
    // Check if the experience has changed from the original
    const originalExperience = sport.experienceSelected || "Beginner";
    setHasExperienceChanged(experience !== originalExperience);
  };

  // Determine button state based on selection status and experience changes
  const shouldShowSelectButton = () => {
    // If sport is not selected, always show "Select"
    if (!sport.selected) {
      return true;
    }
    // If sport is selected but experience has changed, show "Select"
    if (sport.selected && hasExperienceChanged) {
      return true;
    }
    // If sport is selected and no changes, show "Selected"
    return false;
  };

  const onCloseModal = () => {
    setHasExperienceChanged(false);
    onClose();
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onCloseModal}
      className="hidden w-full max-w-md text-white border-none rounded-lg lg:block bg-gradient-to-b from-[#740F1C] to-[#040031] backdrop-blur-md"
    >
      <div className="flex flex-col gap-6 p-4">
        {/* Header with Icon and Sport Info */}
        <div className="flex items-start gap-4 p-4 rounded-lg bg-[#FDFDFD1A] backdrop-blur-sm">
          <img
            src={sport.icon}
            alt={sport.name}
            className="object-contain w-16 h-16 my-auto"
          />
          <div className="flex-1">
            <h2 className="mb-2 text-xl font-bold text-white">{sport.name}</h2>
            <p className="text-sm leading-relaxed text-white/80">
              {sport.description}
            </p>
          </div>
        </div>

        {/* Experience Level Selection */}
        <div className="space-y-4">
          <h3 className="text-sm font-medium text-white/90">
            Experience level
          </h3>

          {/* Experience Level Slider */}
          <div className="relative">
            <div className="flex items-center justify-between mb-2">
              {experienceLevels.map((level) => (
                <button
                  key={level}
                  onClick={() => handleExperienceChange(level)}
                  className={`text-xs font-medium transition-colors ${
                    selectedExperience === level
                      ? "text-white"
                      : "text-white/50"
                  }`}
                >
                  {level}
                </button>
              ))}
            </div>

            {/* Slider Track */}
            <div className="relative">
              <Progress
                
                value={
                  selectedExperience === "Beginner"
                    ? 0
                    : selectedExperience === "Intermediate"
                    ? 50
                    : 100
                }
                className="h-1 bg-white/20"
              />
              {/* Slider Thumb */}
              <div
                className="absolute w-4 h-4 bg-white rounded-full -top-1.5 transition-all duration-300 shadow-lg"
                style={{
                  left:
                    selectedExperience === "Beginner"
                      ? "0"
                      : selectedExperience === "Intermediate"
                      ? "calc(50% - 8px)"
                      : "calc(100% - 8px)",
                }}
              />
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-3 mt-4">
          <Button
            onClick={onCloseModal}
            className="flex-1 text-white bg-transparent border-none hover:bg-white/10"
          >
            Cancel
          </Button>
          {shouldShowSelectButton() ? (
            <Button
              onClick={handleConfirm}
              variant="white"
              className="flex-1 font-semibold text-blue-900 bg-white hover:bg-white/90"
            >
              Select
            </Button>
          ) : (
            <Button
              onClick={handleRemove}
              className="flex items-center justify-center flex-1 gap-2 font-semibold text-white bg-transparent border border-white/50 hover:bg-white/10 rounded-xl"
            >
              <DoubleTickIcon className="text-white" />
              Unselect
            </Button>
          )}
        </div>
      </div>
    </Modal>
  );
}

export default SelectSportsModal;
