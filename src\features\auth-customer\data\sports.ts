import type { Sport, SportOption } from "../type";
import FlagFootball from "@/assets/flag-football.svg";
import Soccer from "@/assets/soccer.png";
export const sportsOptions: SportOption[] = [
  { value: "4", label: "Soccer" },
  { value: "5", label: "Pickleball" },
  { value: "1", label: "Padel" },
  { value: "2", label: "Tennis" },
  { value: "6", label: "Running" },
  { value: "3", label: "Flag football" },
  { value: "7", label: "Volley ball" },
  { value: "8", label: "Field Hockey" },
];

export const sportsData: Sport[] = [
  {
    _id: "4",
    name: "Soccer",
    description:
      "A dynamic mix of tennis and squash, played in doubles on an enclosed court with walls that keep the ball in play.",
    experienceSelected: null,
    selected: false,
    icon: Soccer,
    category: "outdoor",
    color: "#060042",
  },
  {
    _id: "1",
    name: "Padel",
    icon: FlagFootball,
    description:
      "A dynamic mix of tennis and squash, played in doubles on an enclosed court with walls that keep the ball in play.",
    experienceSelected: null,
    selected: false,
    category: "indoor",
    color: "#002000",
  },
  {
    _id: "5",
    name: "Pickleball",
    icon: Soccer,
    description:
      "A dynamic mix of tennis and squash, played in doubles on an enclosed court with walls that keep the ball in play.",
    experienceSelected: null,
    selected: false,
    category: "indoor",
    color: "#060042",
  },
  {
    _id: "6",
    name: "Running",
    icon: Soccer,
    description:
      "A dynamic mix of tennis and squash, played in doubles on an enclosed court with walls that keep the ball in play.",
    experienceSelected: null,
    selected: false,
    category: "outdoor",
    color: "#060042",
  },
  {
    _id: "2",
    name: "Tennis",
    icon: FlagFootball,
    description:
      "A dynamic mix of tennis and squash, played in doubles on an enclosed court with walls that keep the ball in play.",
    experienceSelected: null,
    selected: false,
    category: "indoor",
    color: "#060042",
  },
  {
    _id: "7",
    name: "Volley ball",
    icon: Soccer,
    description:
      "A dynamic mix of tennis and squash, played in doubles on an enclosed court with walls that keep the ball in play.",
    experienceSelected: null,
    selected: false,
    category: "indoor",
    color: "#060042",
  },
  {
    _id: "3",
    name: "Flag football",
    icon: FlagFootball,
    description:
      "A dynamic mix of tennis and squash, played in doubles on an enclosed court with walls that keep the ball in play.",
    experienceSelected: null,
    selected: false,
    category: "outdoor",
    color: "#060042",
  },
  {
    _id: "8",
    name: "Field Hockey",
    icon: Soccer,
    description:
      "A dynamic mix of tennis and squash, played in doubles on an enclosed court with walls that keep the ball in play.",
    experienceSelected: null,
    selected: false,
    category: "outdoor",
    color: "#060042",
  },
];