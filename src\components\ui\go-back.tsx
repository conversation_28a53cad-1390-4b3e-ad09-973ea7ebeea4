import { useNavigate } from "react-router-dom";
import BackArrowIcon from "../icons/backArrowIcon";

function GoBack({ href }: { href?: string }) {
  const navigate = useNavigate();
  const handleGoBack = () => {
    if (href) {
      navigate(href);
      return;
    }
    if (window.history.length > 1) {
      navigate(-1);
    } else {
      navigate("/"); // fallback to home
    }
  };
  return (
    <div
      onClick={handleGoBack}
      className="flex items-center gap-x-1 my-5 cursor-pointer "
    >
      <BackArrowIcon className="w-3 h-3 " />
      <span className="font-semibold"> Go back</span>
    </div>
  );
}

export default GoBack;
