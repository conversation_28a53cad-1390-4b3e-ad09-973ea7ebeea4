import { BrowserRouter, Route, Routes } from "react-router-dom";
import "./index.css";
import CustomerRoutes from "./routes/CustomerRoutes";

function App() {
  return (
    <>
      <BrowserRouter>
        {/* <ErrorBoundary fallback={<ErrorPage />}>
          <ScrollToTop /> */}
        <Routes>
          <Route path="/*" element={<CustomerRoutes />}></Route>
        </Routes>
        {/* </ErrorBoundary> */}
      </BrowserRouter>
    </>
  );
}

export default App;
