import UniverseIcon from "../icons/universeIcon";
import VideoIcon from "../icons/videoIcon";
import ShopIcon from "../icons/shopIcon";
import GroupIcon from "../icons/groupIcon";
import CalendarIcon from "../icons/calendarIcon";
import { Link, useLocation } from "react-router-dom";
function CustomerFooter() {
  const location = useLocation();
  const footerItems = [
    { label: "Universe", href: "/home", icon: UniverseIcon },
    { label: "Videos", href: "/videos", icon: VideoIcon },
    { label: "Groups", href: "/groups", icon: GroupIcon },
    { label: "Calendar", href: "/calendar", icon: CalendarIcon },
    { label: "Shop", href: "/shop", icon: ShopIcon },
  ];
  const isActive = (path: string) => location.pathname === path;
  // bg-gradient-to-b from-[#9999994D] via-[#14141473] to-[#0F0F0F] from-1% via-45% to-100%
  return (
    <div className="h-screen">
      <footer className="w-full border-t border-gray-500  flex justify-between p-3  z-10 fixed bottom-0 rounded-t-lg bg-transparent drop-shadow-md  backdrop-blur-[6px]">
        {footerItems.map((item) => (
          <Link to={item.href} key={item.label}>
            <div
              className={`${
                isActive(item.href)
                  ? "border-t-2 border-r border-b-2 border-l border-gray-500 backdrop-blur-md"
                  : ""
              } flex flex-col gap-y-1 items-center p-2 rounded-xl`}
            >
              <item.icon className="" />
              <h5
                className={`text-xs ${
                  isActive(item.href) ? "text-white" : "text-white-75"
                }  `}
              >
                {item.label}
              </h5>
            </div>
          </Link>
        ))}
      </footer>
    </div>
  );
}

export default CustomerFooter;
