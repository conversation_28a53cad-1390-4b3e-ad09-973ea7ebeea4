import BrandLogo from "@/assets/logo.png";
import CustomerAuthLayout from "@/components/layout/CustomerAuthLayout";
import { But<PERSON> } from "@/components/ui/button";
import { Link } from "react-router-dom";
import SportSelection from "../components/SportSelection";
import LeftIcon from "@/components/icons/leftIcon";
import SearchIcon from "@/components/icons/searchIcon";
import { Input } from "@/components/ui/input";
import { useState } from "react";
function SelectSports() {
  const [showSearch, setShowSearch] = useState(false);
  const handleSearchClick = () => {
    setShowSearch(!showSearch);
  };
  return (
    <CustomerAuthLayout>
      {/* Header */}
      <div className="flex-shrink-0 w-full px-4 py-6 sm:px-6 lg:px-10">
        {/* Desktop Header */}
        <div className="items-center justify-between hidden gap-4 lg:flex">
          <div>
            <img src={BrandLogo} alt="ub sports" className="w-auto h-8" />
          </div>{" "}
          <Link to="/home">
            <Button className="px-6 text-center " variant={"default"}>
              Continue to UB Universe
            </Button>
          </Link>
        </div>
        {/* Mobile Header */}
        <div className="flex flex-col gap-y-4 lg:hidden">
          <div className="flex items-center gap-x-4">
            <LeftIcon className="text-white" />
            <h1 className="text-xl font-bold text-white grow">
              Select sports to track
            </h1>
            <button onClick={handleSearchClick}>
              <SearchIcon className="text-white" />
            </button>
          </div>
          {/* search section */}
          {showSearch && (
            <div className="relative">
              <SearchIcon className="absolute z-10 -translate-y-1/2 left-4 top-1/2 text-white/60" />
              <Input
                type="text"
                variant="transparent"
                className="w-full pl-12 text-white backdrop-blur-0 placeholder:text-white/60"
              />
            </div>
          )}
          {/* search section ends */}
          <p className="text-sm leading-relaxed text-white-85">
            Your email has been confirmed. Welcome to the UB Sports universe!
          </p>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 px-4 pb-8 lg:mt-6 ">
        <div className="max-w-md mx-auto lg:max-w-max">
          <SportSelection />
        </div>
      </div>
    </CustomerAuthLayout>
  );
}

export default SelectSports;
