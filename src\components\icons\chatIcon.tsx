function chatIcon({ className }: { className?: string }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      className={className}
    >
      <path
        d="M5.96999 7.88601H14.03M5.96999 12.114H11.718M9.99999 19.25C12.0094 19.2496 13.964 18.5948 15.5682 17.3848C17.1724 16.1748 18.339 14.4753 18.8915 12.5434C19.444 10.6115 19.3523 8.55214 18.6305 6.6769C17.9086 4.80167 16.5957 3.21249 14.8904 2.14971C13.185 1.08694 11.18 0.608368 9.17854 0.786379C7.17705 0.96439 5.28796 1.7893 3.79697 3.13636C2.30597 4.48341 1.29416 6.27934 0.914556 8.25255C0.534953 10.2258 0.808205 12.2689 1.69299 14.073C1.80099 14.293 1.83699 14.541 1.78199 14.779L0.965987 18.315C0.942982 18.4142 0.945621 18.5177 0.973654 18.6156C1.00169 18.7136 1.05419 18.8027 1.12622 18.8748C1.19825 18.9468 1.28743 18.9993 1.38536 19.0273C1.4833 19.0554 1.58675 19.058 1.68599 19.035L5.22099 18.218C5.45966 18.1657 5.70906 18.1975 5.92699 18.308C7.19438 18.9304 8.58802 19.2527 9.99999 19.25Z"
        stroke="#FDFDFD"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
}

export default chatIcon;
